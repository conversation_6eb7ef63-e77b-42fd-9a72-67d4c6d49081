import { api1Request } from '@/utils/request'

// 参数接口
export interface TransferCouponParams {
  /*转赠优惠券ID */
  couponId?: number
  /*转赠数量 */
  transferNum?: number
  /*限制领取数量 */
  limitReceiveNum?: number
  /*留言 */
  message?: string
}

// 响应接口
export interface TransferCouponRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 转赠优惠券
 * @param {object} params 转赠优惠券
 * @param {number} params.couponId 转赠优惠券ID
 * @param {number} params.transferNum 转赠数量
 * @param {number} params.limitReceiveNum 限制领取数量
 * @param {string} params.message 留言
 * @returns
 */
export function transferCoupon(params: TransferCouponParams): Promise<TransferCouponRes> {
  return api1Request.post({
    url: `/api/coupon/transfer`,
    data: params
  })
}

// 响应接口
export interface CouponReceiveRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 领取优惠券
 * @param {string} couponIds 多个优惠券ID，多个逗号分隔
 * @returns
 */
export function couponReceive(couponIds: string): Promise<CouponReceiveRes> {
  return api1Request.post({
    url: `/api/coupon/receive?couponIds=${couponIds}`
  })
}

// 响应接口
export interface ReceiveTransferCouponRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 领取转赠优惠券
 * @param {string} code 优惠券code
 * @param {string} openId 微信OpenId
 * @returns
 */
export function receiveTransferCoupon(code: string, openId: string): Promise<ReceiveTransferCouponRes> {
  return api1Request.post({
    url: `/api/coupon/receive/transfer/coupon?code=${code}&openId=${openId}`
  })
}

// 响应接口
export interface ReceiveListRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 代金券列表
 * @param {string} pageNum 页码
 * @param {string} pageSize 每页数量
 * @returns
 */
export function receiveList(pageNum: string, pageSize: string): Promise<ReceiveListRes> {
  return api1Request.get({
    url: `/api/coupon/valid/list?pageNum=${pageNum}&pageSize=${pageSize}`
  })
}

// 响应接口
export interface TransferDetailRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 转赠详情
 * @param {string} code 转赠优惠券返回的data数据
 * @returns
 */
export function transferDetail(code: string): Promise<TransferDetailRes> {
  return api1Request.get({
    url: `/api/coupon/transfer/detail?code=${code}`
  })
}

// 响应接口
export interface IndexPopRes {
  code?: number
  msg?: string
  data?: any
}

/**
 * 首页弹窗优惠券
 * @returns
 */
export function indexPop(): Promise<IndexPopRes> {
  return api1Request.get({
    url: `/api/coupon/index/pop`
  })
}

// 响应接口
export interface ExpiredListRes {
  code?: number
  msg?: string
  data?: Array<any>
}

/**
 * 失效代金券列表
 * @returns
 */
export function expiredList(): Promise<ExpiredListRes> {
  return api1Request.get({
    url: `/api/coupon/expired/list`
  })
}
