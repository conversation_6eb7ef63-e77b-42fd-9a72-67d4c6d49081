import { useEffect } from 'react'
import { Input } from '@tarojs/components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { useAsyncFn, useUpdateEffect } from 'react-use'
import { orderCreate, orderCreatInfo, OrderCreatInfoRes } from '@/api/order'
import addrImg from '@/assets/images/order/addr.png'
import moveImg from '@/assets/images/order/move.png'
import warnImg from '@/assets/images/order/warn.png'
import { wxPay } from '@/utils'
import useObjState from '@/hooks/useObjState'
import { computePrice, ComputePriceRes } from '@/api/detail'
import NavBarTitle from './components/NavBarTitle'
import SubmitBtn from './components/SubmitBtn'

const tabs: { name: string; key: 'readyImageList' | 'printingImageList' }[] = [
  {
    name: '成衣',
    key: 'readyImageList'
  },
  {
    name: '印花',
    key: 'printingImageList'
  }
]

const Pay = () => {
  const activeTab = useObjState<'readyImageList' | 'printingImageList'>('readyImageList')
  const previewImg = useObjState('') // 预览图
  const selectSize = useObjState('') // 选中的尺码
  const num = useObjState<number | ''>(1) // 数量，默认为1
  const compute = useObjState<ComputePriceRes['data']>({
    totalNum: 0,
    totalPrice: 0,
    frontPrintingId: 0,
    frontPrinting: '',
    frontArea: 0,
    frontPrice: 0,
    backPrintingId: 0,
    backPrinting: '',
    backArea: 0,
    backPrice: 0,
    basePrice: 0
  })
  const userAddress = useObjState<OrderCreatInfoRes['data']['userAddress'] | null>(null)

  const router = useRouter()

  const [orderCreatInfoState, orderCreatInfoFetch] = useAsyncFn(async () => {
    console.log(router.params.id)
    if (!router.params.id) return
    const res = await orderCreatInfo(router.params.id)
    console.log('response', res)
    previewImg.set(res.data.readyImageList[0])
    selectSize.set(res.data.costDetail.sizeCode)
    compute.set(res.data.costDetail)
    res.data.userAddress && userAddress.set(res.data.userAddress)
    return res.data
  }, [])

  useEffect(() => {
    orderCreatInfoFetch()
  }, [])

  const [computePriceState, computePriceFetch] = useAsyncFn(async () => {
    if (!orderCreatInfoState.value || !selectSize.val) return
    const diyData = JSON.parse(orderCreatInfoState.value.diyData)
    const res = await computePrice({
      gender: orderCreatInfoState.value.gender,
      masterTemplateCode: orderCreatInfoState.value.templateCode,
      styleTemplateCode: orderCreatInfoState.value.styleCode,
      frontDiyData: JSON.stringify(diyData[0]) || '',
      backDiyData: JSON.stringify(diyData[1]) || '',
      frontPrintingId: orderCreatInfoState.value.costDetail.frontPrintingId,
      backPrintingId: orderCreatInfoState.value.costDetail.backPrintingId,
      buyNum: 1,
      sizeCode: selectSize.val
    })
    console.log('response', res)
    if (res.code !== 200) {
      Taro.showToast({
        title: res.msg,
        icon: 'none'
      })
      return null
    }
    compute.set(res.data)
    return res
  }, [orderCreatInfoState.value, selectSize.val])

  useUpdateEffect(() => {
    computePriceFetch()
  }, [orderCreatInfoState.value, selectSize.val])

  const [orderCreateState, orderCreateFetch] = useAsyncFn(async () => {
    if (!userAddress.val) {
      return 0
    }
    if (!router.params.id || !orderCreatInfoState.value) return
    const res = await orderCreate({
      openId: Taro.getStorageSync('__weapp_open_id__'),
      couponId: 0, // TODO: 优惠券
      designTemplateId: Number(router.params.id),
      size: selectSize.val,
      costDetail: {
        ...compute.val,
        totalNum: num.val || 1
      },
      addressId: userAddress.val.id,
      totalPrice: compute.val.totalPrice * (num.val || 1)
    })

    return res.data
  }, [orderCreatInfoState.value, selectSize.val, num.val, userAddress.val])

  const pay = async () => {
    // 创建订单
    const orderId = await orderCreateFetch()
    console.log('orderId', orderId)
    if (orderId === 0) {
      Taro.showToast({
        title: '请填选择收货地址',
        icon: 'none'
      })
      return
    }
    if (!orderId) {
      Taro.showToast({
        title: '订单创建失败',
        icon: 'none'
      })
      return
    }
    // 获取支付参数
    const payCode = await wxPay(orderId)
    // if (payCode === 1) {
    // 无论成功与否都跳转到订单列表，不然用户会一直创建重复订单
    // 重新获取订单列表
    Taro.navigateTo({
      url: '/pages/order/index'
    })
    // }
  }

  useDidShow(async () => {
    const selectedAddress = await new Promise((resolve) => {
      Taro.getStorage({ key: 'selectedAddress' })
        .then((res) => {
          console.log(res)
          resolve(res.data)
        })
        .catch((err) => {
          console.log(err)
          resolve(null)
        })
    })

    if (selectedAddress) {
      userAddress.set(selectedAddress as OrderCreatInfoRes['data']['userAddress'])
      // 更新订单地址
      Taro.removeStorageSync('selectedAddress')
    }
  })

  const fixNum = num.val || 1

  console.log('userAddress.val', userAddress.val)

  return (
    <>
      <div className="w-full h-screen overflow-hidden flex flex-col bg-[#F8F8F8]">
        <NavBarTitle />
        {orderCreatInfoState.value && (
          <div className="relative flex-1 overflow-auto bg-[#F8F8F8]">
            <div className="z-0 h-[870px] w-full bg-[#E5E7E9] box-border pb-[30px] relative flex_center">
              <img className="w-[566px] h-[566px] object-contain" src={previewImg.val} alt="" />
              <div className="absolute bottom-[48px] left-0 w-full h-[190px]">
                <div className="h-[40px] flex px-[24px] box-border">
                  {tabs.map((tab) => {
                    return (
                      <div
                        key={tab.key}
                        className={`w-[100px] h-[40px] flex_center rounded-full font-normal text-[20px] text-black leading-[20px] text-center not-italic ${activeTab.val === tab.key ? 'bg-white' : ''}`}
                        onClick={() => {
                          activeTab.set(tab.key)
                          if (orderCreatInfoState.value) {
                            previewImg.set(
                              tab.key === 'readyImageList'
                                ? orderCreatInfoState.value.readyImageList[0]
                                : orderCreatInfoState.value.printingImageList[0]
                            )
                          }
                        }}
                      >
                        {tab.name}
                      </div>
                    )
                  })}
                </div>
                {activeTab.val === 'readyImageList' ? (
                  <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
                    {orderCreatInfoState.value.readyImageList.map((cheng, index) => (
                      <div
                        key={index}
                        className="w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid border-[rgba(0,0,0,0.1)] flex_center mr-[12px]"
                      >
                        <img className="w-[112px] h-[112px] object-contain" src={cheng} alt="" />
                      </div>
                    ))}
                  </div>
                ) : null}
                {activeTab.val === 'printingImageList' ? (
                  <div className="mt-[20px] flex overflow-x-auto no-scrollbar px-[24px]">
                    {orderCreatInfoState.value.printingImageList.map((hua, index) => (
                      <div
                        key={index}
                        className="w-[130px] h-[128px] rounded-[20px] bg-[#AFBBC7] border-2 border-solid border-[rgba(0,0,0,0.1)] flex_center mr-[12px]"
                      >
                        <img className="w-[112px] h-[112px] object-contain" src={hua} alt="" />
                      </div>
                    ))}
                  </div>
                ) : null}
              </div>
            </div>
            <div className="z-10 relative mt-[-30px] bg-white w-full shadow-[0px_4_10px_0px_rgba(0,0,0,0.06)] rounded-[24px_24px_0_0] px-[25px] box-border pt-[28px] pb-[18px]">
              <div
                onClick={() => {
                  Taro.navigateTo({
                    url: '/pages/address/index?isCheck=true'
                  })
                }}
                className="h-[80px] flex_center mb-[8px]"
              >
                <img className="w-[32px] h-[32px] self-start mt-[3px]" src={addrImg} alt="" />
                {userAddress.val ? (
                  <div className="h-full ml-[10px] mr-[20px] flex-1 w-0 font-medium text-[28px] text-black leading-[40px] text-left not-italic line-clamp-2">
                    {userAddress.val.contacts} {userAddress.val.phone} {userAddress.val.province}
                    {userAddress.val.city}
                    {userAddress.val.area}
                    {userAddress.val.street}
                    {userAddress.val.detail}
                  </div>
                ) : (
                  <div className="h-full ml-[10px] mr-[20px] flex-1 w-0 font-medium text-[28px] text-black leading-[40px] text-left not-italic line-clamp-2">
                    请选择收货地址
                  </div>
                )}
                <img className="w-[32px] h-[32px]" src={moveImg} alt="" />
              </div>
              <div className="font-normal text-[24px] text-black opacity-60 leading-[34px] text-left not-italic mb-[34px] box-border pl-[42px]">
                {orderCreatInfoState.value.expressTips}
              </div>
              <div className="w-[680px] h-[2px] opacity-10 bg-black"></div>

              <div className="flex items-start mt-[44px] mb-[10px]">
                <div className="w-[84px] h-[60px] font-medium text-[28px] text-black leading-[60px] text-left not-italic mr-[30px]">
                  尺码：
                </div>
                <div className="flex flex-wrap flex-1 w-0">
                  {orderCreatInfoState.value.patternSize.map((size) => (
                    <div
                      key={size.id}
                      onClick={() => selectSize.set(size.size)}
                      className={`mr-[20px] mb-[30px] w-[120px] h-[60px] rounded-[8px] bg-[#F8F8F8] border-2 border-solid flex_center font-normal text-[24px] text-black leading-[34px] text-left not-italic ${selectSize.val === size.size ? 'border-[#000000]' : 'border-[rgba(0,0,0,0.1)]'}`}
                    >
                      {size.size}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center mb-[38px]">
                <div className="w-[84px] h-[60px] font-medium text-[28px] text-black leading-[60px] text-left not-italic mr-[30px]">
                  数量：
                </div>
                <div className="flex flex-1 w-0">
                  <div className="w-[226px] h-[60px] rounded-[8px] bg-[#F8F8F8] border-2 border-solid border-[rgba(0,0,0,0.1)] flex_center">
                    <div onClick={() => num.set(fixNum - 1 < 1 ? 1 : fixNum - 1)} className="w-[50px] flex_center">
                      -
                    </div>
                    <div className="w-[2px] h-[34px] opacity-20 bg-black"></div>
                    <Input
                      onInput={(e) =>
                        num.set(Number(e.detail.value) < 1 ? '' : Number(e.detail.value) > 9999 ? 9999 : Number(e.detail.value))
                      }
                      onBlur={() => num.set(fixNum < 1 ? 1 : fixNum)}
                      value={`${num.val}`}
                      type="number"
                      maxlength={4}
                      className="flex-1 w-0 flex_center font-normal text-[28px] text-black leading-[20px] text-center not-italic"
                    />
                    <div className="w-[2px] h-[34px] opacity-20 bg-black"></div>
                    <div onClick={() => num.set(fixNum + 1 > 9999 ? 9999 : fixNum + 1)} className="w-[50px] flex_center">
                      +
                    </div>
                  </div>
                </div>
              </div>

              <div className="w-[680px] h-[2px] opacity-10 bg-black mb-[44px]"></div>

              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">T恤： 件数 x{num.val}</div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.basePrice * fixNum) / 100}
                </div>
              </div>
              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                  正面：{compute.val.frontPrinting}
                  <span className="text-[#9f9f9f]">{compute.val.frontArea}cm</span>
                </div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.frontPrice * fixNum) / 100}
                </div>
              </div>
              <div className="flex justify-between mb-[24px]">
                <div className="font-normal text-[24px] text-black leading-[34px] text-left not-italic">
                  背面：{compute.val.backPrinting}
                  <span className="text-[#9f9f9f]">{compute.val.backArea}cm</span>
                </div>
                <div className="font-medium text-[28px] text-black leading-[40px] text-right not-italic">
                  ¥{(compute.val.backPrice * fixNum) / 100}
                </div>
              </div>

              <div className="flex justify-between mb-[50px]">
                <div className="font-normal text-[24px] text-[#E40633] leading-[34px] text-left not-italic">总计：</div>
                <div className="font-medium text-[36px] text-[#E40633] leading-[40px] text-right not-italic">
                  ¥{(compute.val.totalPrice * fixNum) / 100}
                </div>
              </div>

              <div className="flex items-center">
                <img src={warnImg} className="w-[24px] h-[24px] mr-[12px]" alt="" />
                <div className="font-normal text-[24px] text-[#E40633] leading-[34px] text-left not-italic">
                  {orderCreatInfoState.value.waringTips}
                </div>
              </div>
            </div>
          </div>
        )}
        <SubmitBtn pay={pay} totalPrice={(compute.val.totalPrice * fixNum) / 100} />
      </div>
    </>
  )
}

export default Pay
