import { collectImage } from '@/api/inspiration'
import collectImg from '@/assets/images/inspiration/collect.png'
import collect1Img from '@/assets/images/inspiration/collect1.png'
import useObjAtom from '@/hooks/useObjAtom'
import { productListState } from '@/store/inspiration'
import Taro from '@tarojs/taro'
import { useAsyncFn } from 'react-use'

const Pattern = ({ item }) => {
  const productList = useObjAtom(productListState)

  const [collectState, collectFetch] = useAsyncFn(async () => {
    const res = await collectImage(item.id, item.collect ? '0' : '1')
    console.log('response', res)
    if (res.code === 200) {
      Taro.showToast({
        title: item.collect ? '取消收藏成功' : '收藏成功',
        icon: 'none'
      })
      productList.set((prev) => {
        return prev.map((i) => {
          if (i.id === item.id) {
            return {
              ...i,
              collect: i.collect ? 0 : 1
            }
          }
          return i
        })
      })
    }
    return res
  }, [item])

  return (
    <div key={item.id} className="w-[350px] h-[540px] rounded-[16px] overflow-hidden bg-white flex flex-col">
      <img
        onClick={() => {
          Taro.navigateTo({
            url: `/pages/pattern-detail/index?id=${item.id}`
          })
        }}
        className="w-[350px] h-[350px] rounded-[16px_16px_0px_0px]"
        src={item.imageUrl}
        alt={item.title}
      />
      <div className="flex-1 flex flex-col justify-center px-[16px]">
        <div className="font-normal text-[24px] text-[#202020] leading-[36px] text-justify not-italic mb-[20px] line-clamp-2">
          {item.title}
        </div>
        <div className="flex justify-between items-end">
          <div onClick={collectFetch} className="w-[60px] h-[60px] rounded-[16px] bg-[#F8F8F8] flex_center">
            <img className="w-[42px] h-[40px]" src={item.collect ? collect1Img : collectImg} alt="" />
          </div>
          <div
            onClick={() => {
              Taro.navigateTo({
                url: `/pages/pattern-detail/index?id=${item.id}`
              })
            }}
            className="w-[238px] h-[60px] rounded-[16px] bg-[#F8F8F8] flex_center"
          >
            <span className="font-medium text-[20px] text-black leading-none text-center not-italic mr-[8px]">设计同款</span>
            <span className="font-normal text-[16px] text-black leading-none text-center not-italic">{item.customNum}万人</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Pattern
