import ConputItem from './components/ConpouItem'
import UseConputItem from './components/UseConpouItem'
import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { activePathState } from '@/store/global'
import { useState, useEffect } from 'react'
import Taro, { useRouter } from '@tarojs/taro'
import { transferDetail, receiveTransferCoupon } from '@/api/coupon'
import dayjs from 'dayjs'
import './index.scss'

const Index = () => {
  const activePath = useObjAtom(activePathState)
  // 是否领取优惠券
  const [isGet, setGetVisible] = useState(false)
  const [conpouInfo, setCouponInfo] = useState<any>([])
  // 优惠券领取成功弹窗
  const [successVisible, setSuccessVisible] = useState(false)

  const router = useRouter()
  const { key = '' } = router.params

  // 获取优惠券详情
  const getCouponInfo = async (code) => {
    const res = await transferDetail(code)
    console.log('获取优惠券详情', res.data)
    setCouponInfo(res.data)
    if (res.data.ticked == 0) {
      setGetVisible(false)
    } else {
      setGetVisible(true)
    }
  }

  // 领取优惠券
  const receiveCouponData = async () => {
    let openId = ''
    Taro.getStorage({
      key: '__weapp_open_id__', // 缓存数据的键名
      success: (res) => {
        openId = res.data // 读取的数据在 res.data 中
      },
      fail: (err) => {
        console.error('获取失败:', err)
      },
      complete: () => {
        console.log('调用完成')
      }
    })
    const res = await receiveTransferCoupon(key, openId)
    console.log('领取好友发送的优惠券', res.data)
    if (res.code === 200) {
      setSuccessVisible(true)
    }
  }

  useEffect(() => {
    getCouponInfo(key)
  }, [])
  return (
    <>
      <div className="bg">
        <div className="dialog">
          <div className="dialogTitle">好友礼赠</div>
          <div className="ribbon"></div>
          <div className="time">吉吉赠送于7月21日10:10</div>
          {!isGet && <ConputItem conpouInfo={conpouInfo}></ConputItem>}
          {!isGet && (
            <div className="drawBtn" onClick={() => receiveCouponData()}>
              立即领取
            </div>
          )}
          {isGet && <UseConputItem conpouInfo={conpouInfo}></UseConputItem>}
          {isGet && <div className="geted">代金券已被领完</div>}
        </div>

        {successVisible && (
          <div className="shadowBg">
            <div className="dialogNoupou">
              <div className="hint">代金券领取成功</div>
              <div className="use">已放入「我的-优惠券」中</div>
              <div className="noupon">
                <div className="leftItem">
                  <div className="count">x{conpouInfo?.reduceNum}</div>
                  <div className="content">
                    <div className="moneyCount">
                      {conpouInfo?.reducePrice}
                      <span className="unit">元</span>
                    </div>
                    <div className="xianzhi">{conpouInfo?.couponName}</div>
                  </div>
                </div>
                <div className="rightItem">
                  <div className="title">{conpouInfo?.couponName}</div>
                  <div className="time">
                    {dayjs(conpouInfo?.endTime - conpouInfo?.startTime).format('HH : mm : ss')}
                    <span className="timeText">后过期</span>
                  </div>
                  <div className="useTitle">满{conpouInfo?.fullPrice}金额可用</div>
                </div>
              </div>
              <div
                className="btn"
                onClick={() => {
                  Taro.switchTab({
                    url: '/pages/inspiration/index',
                    success: () => {
                      activePath.set('/pages/inspiration/index')
                    }
                  })
                }}
              >
                去使用
              </div>

              <div className="closeBtn" onClick={() => setSuccessVisible(false)}>
                ×
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default Index
