import { Popup, Input, Radio, Notify } from '@taroify/core'
import { useState, useEffect } from 'react'
import { useAsyncFn } from 'react-use'
import SelectConpou from './components/selectConpou'
import { receiveList, transferCoupon } from '../../api/coupon'
import useObjState from '@/hooks/useObjState'
import dayjs from 'dayjs'
import { useShareAppMessage } from '@tarojs/taro'
import './index.scss'

const Index = () => {
  const [open, setOpen] = useState(false)
  const [shartVisible, setShartVisible] = useState(false)

  const [radioValue, setRadioValue] = useState('1')
  const [transferNum, setTransferNum] = useState()
  const [limitReceiveNum, setLimitReceiveNum] = useState()
  const [message, setMessage] = useState('')

  const activeProductGroupId = useObjState(1)
  const isOpen = useObjState({})
  const pageNum = useObjState('1')
  const pageSize = useObjState('10')
  const [nouponList, setNouponList] = useState([])

  // 赠送后接口返回的code分享码
  const [shareCode, setShareCode] = useState()

  // 默认展示优惠券列表第一张
  const [defaultShowNoupon, setDefaultShowNoupon] = useState<any>([])

  const [getOrderList, getOrderListFetch] = useAsyncFn(async () => {
    const params: { pageNum: string; pageSize: string } = {
      pageNum: pageNum.val,
      pageSize: pageSize.val
    }
    const res = await receiveList(params.pageNum, params.pageSize)
    setNouponList(res.data.list)
    setDefaultShowNoupon(res.data.list[0])
    return res
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  // 子组件更换优惠券
  const updateSelectConpouItem = (conputItem) => {
    setDefaultShowNoupon(conputItem)
    setOpen(false)
  }

  // 赠送优惠券数量
  const setTransferNumFn = (e) => {
    if (e.detail.value < 0) return
    if (e.detail.value > defaultShowNoupon.totalNum) return Notify.open({ type: 'danger', message: '赠送代金券数量，不可超过总数' })
    setTransferNum(e.detail.value)
  }

  // 每个领取多张赠送优惠券数量
  const setLimitNumFn = (e) => {
    if (e.detail.value < 0) return
    if (e.detail.value > transferNum) return Notify.open({ type: 'danger', message: '每人领取代金券数量，不可超过总数' })
    setLimitReceiveNum(e.detail.value)
  }

  // 立即赠送
  const sendNoupon = async () => {
    const data = {
      couponId: defaultShowNoupon.id, // 优惠券id
      transferNum: transferNum, // 转赠优惠券数量
      limitReceiveNum: limitReceiveNum, // 限制领取数量
      message: message // 留言
    }
    const res = await transferCoupon(data)
    setShareCode(res.data)
    setShartVisible(true)
  }

  // 分享
  useShareAppMessage(() => {
    return {
      title: message, // 分享卡片的title
      path: `/pages/get-gift/index?key=${shareCode}`, // 分享卡片的小程序路径
      imageUrl: '' // 分享卡片的图片链接
    }
  })

  return (
    <>
      <div className="continer">
        <div className="noupon">
          <div className="toptitle">
            <div className="daijinquan">代金券</div>
            <div className="change" onClick={() => setOpen(true)}>
              更换
            </div>
          </div>
          <div className="couponItem">
            <div className="leftItem">
              <div className="count">x{defaultShowNoupon.totalNum}</div>
              <div className="content">
                <div className="moneyCount">
                  {defaultShowNoupon.reducePrice}
                  <span className="unit">元</span>
                </div>
                <div className="xianzhi">{defaultShowNoupon.couponName}</div>
              </div>
            </div>
            <div className="rightItem">
              <div className="leftContentBox">
                <div className="title">{defaultShowNoupon.couponName}</div>
                <div className="time">
                  {dayjs(defaultShowNoupon.endTime - defaultShowNoupon.startTime).format('HH : mm : ss')}{' '}
                  <span className="timeText">后过期</span>
                </div>
                <div className="useTitle">满{defaultShowNoupon.fullPrice}金额可用</div>
              </div>
            </div>
          </div>
        </div>
        <div className="snedCount">
          <div className="snedAllCount">赠送代金券总数量</div>
          <div className="countInput">
            <Input
              placeholder=""
              className="inputComponent"
              style={{ fontSize: '20px' }}
              value={transferNum}
              onChange={(e) => setTransferNumFn(e)}
            />
            <span>张</span>
          </div>
        </div>
        <div className="setting">
          <div className="title">领取设置</div>
          <Radio.Group value={radioValue} className="custom-color" onChange={(name) => setRadioValue(name)}>
            <Radio name="1">限每人领取1张</Radio>
            <Radio name="2">每人领取多张</Radio>
          </Radio.Group>
          {radioValue == '2' && (
            <div className="countInput_many">
              <Input
                placeholder=""
                className="inputComponent"
                style={{ fontSize: '20px' }}
                value={limitReceiveNum}
                onChange={(e) => setLimitNumFn(e)}
              />
              <span>张</span>
            </div>
          )}
        </div>
        <div className="advice">
          <div className="adviceTitle">礼赠留言</div>
          <div className="adviceInput">
            <Input
              placeholder="请输入"
              className="inputComponent"
              style={{ fontSize: '20px' }}
              value={message}
              onChange={(e) => setMessage(e.detail.value)}
            />
          </div>
        </div>
        <div className="beizhu">注：赠送后无法取消，请谨慎操作</div>
        <div
          className="sendBtn"
          onClick={() => {
            sendNoupon()
          }}
        >
          立即赠送
        </div>
      </div>

      <Popup open={open} placement="bottom" style={{ height: '70%' }}>
        <SelectConpou
          updateSelectConpouItem={updateSelectConpouItem}
          updateVisible={() => setOpen(false)}
          nouponList={nouponList}
        ></SelectConpou>
      </Popup>

      <Popup open={shartVisible} placement="bottom" style={{ height: '30%' }} rounded onClose={() => setShartVisible(false)}>
        <Popup.Close />
        <div className="shareTitle">
          <div className="text">分享</div>
        </div>
        <button open-type="share" className="shareBtn">
          <div className="icon"></div>
          <div className="text">微信好友</div>
        </button>
      </Popup>
    </>
  )
}

export default Index
