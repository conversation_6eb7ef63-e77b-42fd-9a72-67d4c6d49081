import { designTemplateList } from '@/api/my'
import useObj<PERSON><PERSON> from '@/hooks/useObjAtom'
import { myProductListState } from '@/store/my'
import Taro from '@tarojs/taro'
import { useEffect } from 'react'
import { useAsyncFn } from 'react-use'

const Product = () => {
  const myProductList = useObjAtom(myProductListState)

  const [designTemplateListState, designTemplateListFetch] = useAsyncFn(async () => {
    const res = await designTemplateList(1, 200)
    console.log('response', res)
    myProductList.set(res.data.list)
    return res
  }, [])

  useEffect(() => {
    designTemplateListFetch()
  }, [])

  return (
    <div className="flex flex-col h-full">
      <div className="overflow-auto flex-1">
        <div className="flex flex-wrap gap-[12px] p-[19px_19px_0]">
          {myProductList.val.map((item) => (
            <div
              key={item.id}
              onClick={() => {
                Taro.navigateTo({
                  url: `/pages/canvas/index?id=${item.id}&from=own`
                })
              }}
              className="w-[350px] h-[430px] overflow-hidden bg-white flex flex-col"
            >
              <img className="w-[350px] h-[350px] rounded-[16px]" src={item.imageUrl.split(';')[0]} alt={item.title} />
              <div className="flex-1 flex flex-col justify-center">
                <div className="font-normal text-[28px] text-black leading-[40px] not-italic mb-[8px] c-line-clamp-2">{item.title}</div>
              </div>
            </div>
          ))}
        </div>
        {/* - END - */}
        <div className="my-[38px] w-full font-normal text-[20px] text-[#202020] leading-[44px] not-italic flex_center">- END -</div>
      </div>
    </div>
  )
}

export default Product
