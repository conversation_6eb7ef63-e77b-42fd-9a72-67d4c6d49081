import ConputItem from './components/ConpouItem'
import './index.scss'
import { expiredList } from '../../api/coupon'
import { useAsyncFn } from 'react-use'
import { useEffect, useState } from 'react'
import useObjState from '@/hooks/useObjState'
import Taro from '@tarojs/taro'

const Index = () => {
  const activeProductGroupId = useObjState(1)
  const isOpen = useObjState({})
  const pageNum = useObjState('1')
  const pageSize = useObjState('10')
  // 失效优惠券列表
  const [noUsenouponList, setNoUseCouponList] = useState<any>([])

  const [getOrderListState, getOrderListFetch] = useAsyncFn(async () => {
    const res = await expiredList()
    console.log('response', res.data)
    setNoUseCouponList(res.data)
    return res
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  return (
    <>
      {noUsenouponList.map((item) => (
        <ConputItem key={item.id} data={item}></ConputItem>
      ))}

      <div className="end">- END -</div>
    </>
  )
}

export default Index
