import { receiveList } from '../../api/coupon'
import { useAsyncFn } from 'react-use'
import { useEffect, useState } from 'react'
import useObjState from '@/hooks/useObjState'
import ConpouItem from './components/ConpouItem'
import Taro from '@tarojs/taro'
import './index.scss'

const Index = () => {
  const activeProductGroupId = useObjState(1)
  const isOpen = useObjState({})
  const pageNum = useObjState('1')
  const pageSize = useObjState('10')
  const [nouponList, setNouponList] = useState([])

  const [getOrderList, getOrderListFetch] = useAsyncFn(async () => {
    const params: { pageNum: string; pageSize: string } = {
      pageNum: pageNum.val,
      pageSize: pageSize.val
    }
    const res = await receiveList(params.pageNum, params.pageSize)
    console.log('response', res.data)
    setNouponList(res.data.list)
    return res
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  useEffect(() => {
    getOrderListFetch()
  }, [activeProductGroupId.val, pageNum.val, pageSize.val])

  return (
    <>
      {nouponList.map((item) => (
        <ConpouItem key={item} data={item}></ConpouItem>
      ))}

      <div
        className="checkNoUseCoupon"
        onClick={() =>
          Taro.navigateTo({
            url: '/pages/no-usecoupon/index'
          })
        }
      >
        查看无效券
      </div>
    </>
  )
}

export default Index
