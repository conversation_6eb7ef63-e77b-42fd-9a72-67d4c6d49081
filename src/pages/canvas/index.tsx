import useObj<PERSON>tom from '@/hooks/useObjAtom'
import { userinfoState } from '@/store/global'
import { WebView } from '@tarojs/components'
import Taro, { useRouter } from '@tarojs/taro'

const TShirtCanvas = () => {
  const router = useRouter()
  const { id, threadId, messageId } = router.params
  let token = Taro.getStorageSync('__weapp_token__') as string
  const newToken = token.split(' ')
  const tokenEnd = newToken[newToken.length - 1]
  const userinfo = useObjAtom(userinfoState)

  // id // 模板ID
  // userId // 用户ID
  // token
  // threadId // 对话ID
  // messageId // 消息ID
  return (
    <>
      <WebView
        src={`https://test.h5.wode.me/canvas?id=${id || ''}&threadId=${threadId}&messageId=${messageId}&userId=${userinfo.val?.userId || ''}&token=${tokenEnd}`}
      />
    </>
  )
}

export default TShirtCanvas
